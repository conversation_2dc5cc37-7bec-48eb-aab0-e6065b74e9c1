<template>
    <div>
        <img class="bg" src="@/assets/xingyun/bg.png">
        <div class="topBox">
            <img class="logoBox" src="@/assets/xingyun/head.png"></img>
            <img class="popofifi" src="@/assets/xingyun/popofifi.png"></img>
        </div>
        <div class="nowUserBox">
            <div class="item">
                <img class="border" src="@/assets/xingyun/userBox.png"></img>
                <div class="content">
                    <img class="left" :src="nowUserImg" @click="handleImageClick('left')"></img>
                </div>
            </div>
            <div class="item">
                <img class="border" src="@/assets/xingyun/userBox.png"></img>
                <div class="content" v-if="status == 'result'">
                    <img class="border right" :src="userLst[0]?.real" @click="handleImageClick('right')"></img>
                </div>
                <div v-if="status !== 'result'" class="content" ref="videoBox" @click="handleContentClick">
                    <img class="searchLogo" src="@/assets/xingyun/search.png"></img>
                    <base-video
                        :extraStyle="videoConfig"
                        @initFinished="handleInitFinished"
                    ></base-video>
                    <div class="search">
                        <span class="search-left">》》</span>
                        <span class="search-center">搜寻中</span>
                        <span class="search-right">《《</span>
                    </div>
                    <div class="trans"></div>
                </div>
            </div>
        </div>
        <div class="userBoxLst">
            <div v-for="(item, index) of userLst" class="lstItem" :key="index">
                <div class="l" @click="handleUserListClick(item, index)">
                    <img class="border" src="@/assets/xingyun/border.png"></img>
                    <div class="content">
                        <img class="limg" :src="item.result"></img>
                    </div>
                </div>
                <div class="r" @click="handleUserListClick(item, index)">
                    <img class="border" src="@/assets/xingyun/border.png"></img>
                    <div class="content">
                        <img class="rimg" :src="item.real"></img>
                    </div>
                </div>
                <img class="close" src="@/assets/xingyun/close.png" @click="handleCloseClick(index)"></img>
            </div>
            <div class="footer">⏺︎&nbsp;&nbsp; 数据仅用于即时娱乐效果，不进行存储</div>
        </div>

        <!-- 隐藏的图片预加载区域 -->
        <div style="display: none;">
            <img ref="preloadImg1" src="@/assets/xingyun/lihui/1.png" />
            <img ref="preloadImg2" src="@/assets/xingyun/lihui/2.png" />
            <img ref="preloadImg3" src="@/assets/xingyun/lihui/3.png" />
            <img ref="preloadImg4" src="@/assets/xingyun/lihui/4.png" />
            <img ref="preloadImg5" src="@/assets/xingyun/lihui/5.png" />
            <img ref="preloadImg6" src="@/assets/xingyun/lihui/6.png" />
            <img ref="preloadImg7" src="@/assets/xingyun/lihui/7.png" />
            <img ref="preloadImg8" src="@/assets/xingyun/lihui/8.png" />
            <img ref="preloadImg9" src="@/assets/xingyun/lihui/9.png" />
            <img ref="preloadImg10" src="@/assets/xingyun/lihui/10.png" />
            <img ref="preloadImg11" src="@/assets/xingyun/lihui/11.png" />
            <img ref="preloadImg12" src="@/assets/xingyun/lihui/12.png" />
            <img ref="preloadImg13" src="@/assets/xingyun/lihui/13.png" />
            <img ref="preloadImg14" src="@/assets/xingyun/lihui/14.png" />
            <img ref="preloadImg15" src="@/assets/xingyun/lihui/15.png" />
            <img ref="preloadImg16" src="@/assets/xingyun/lihui/16.png" />
            <img ref="preloadImg17" src="@/assets/xingyun/lihui/17.png" />
            <img ref="preloadImg18" src="@/assets/xingyun/lihui/18.png" />
            <img ref="preloadImg19" src="@/assets/xingyun/lihui/19.png" />
        </div>
        <div class="llll" @click="testBtn('l')"></div>
        <div class="rrrr" @click="testBtn('r')"></div>
    </div>

</template>
<script>
import BaseVideo from '@/components/base/Video/index.vue'
import { getPhotoFaceSexAndAge } from '@/apis/face'
import { useConfigStore } from '@/store/config'
import { uploadImg } from '@/views/shishahai_yinyuejie/uploadImg.js'
import { useSinglePhotoStore } from '@/store/single'
import { useRouteParamsStore } from '@/store/routeParams'
import { base64ToBlob } from '@/utils'
import { generateUUID } from '@/utils/uuid'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import electronApi from '@/utils/electronApi'
// 移除import方式的图片引入，改为template中预加载
import switchSoundUrl from '@/assets/xingyun/switch.MP3'



export default {
  data() {
    return {
        status: 'search', // search, loading, result
        userLst: [],
        nowUserImg: '',
        videoConfig: {},
        videoRef: null,
        canvas: null,
        // 人脸检测相关
        faceDetectionInterval: null,
        faceDetectionResult: null,
        // 轮盘相关
        rouletteInterval: null,
        rouletteSpeed: 30, // 初始速度30ms，更快
        rouletteMinSpeed: 1000, // 最低速度1秒
        rouletteCurrentIndex: 0,
        imgList: [], // 将在mounted中初始化
        // 图片生成相关
        generationTaskId: null,
        generationInterval: null,
        selectedGender: 'female', // 默认女性
        avatarId: null,
        objectKey: null,
        // 结果显示相关
        resultTimeout: null, // 10秒自动重置定时器
        resultStartTime: null, // 结果显示开始时间
        currentResultData: null, // 当前结果数据，用于跳转loading页面
        originalPhotoData: null, // 原始照片数据
        detectedPhotoData: null, // 人脸检测时的原始照片数据

        // 需要清理的定时器集合
        timers: {
          initTimeout: null,
          pollTimeout: null
        },

        // 慢速轮盘相关
        slowRouletteInterval: null,
        slowRouletteSpeed: 1000, // 慢速轮盘间隔1秒
        // 猫
        userId: 0,
        tempUserId: 0, //缓存id,等到生成后再算userId
        timeout3031: null,
        serveIp: ''
    };
  },
  components:{BaseVideo},
  watch: {
    // 监听nowUserImg变化，播放切换音效
    nowUserImg(newVal, oldVal) {
      // 只有当值真正改变时才播放音效
      if (newVal && newVal !== oldVal) {
        this.playSwitchSound()
      }
    }
  },
  mounted() {
    
    // 从sessionStorage读取用户列表
    const savedUserLst = sessionStorage.getItem('xingyun_userLst')
    this.userLst = savedUserLst ? JSON.parse(savedUserLst) : []
    console.log(this.userLst)
    // 初始化图片列表，从预加载的img元素中获取src
    this.initImageList()

    this.timers.initTimeout = setTimeout(e=>{
        let box = this.$refs['videoBox']
        console.log(box)
        this.videoConfig = {
                            transform: 'scaleX(-1)',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            width:'100%',
                            height: '100%',
                        }
    },30)

    // 开始人脸检测
    this.startFaceDetection()


    // 开始慢速轮盘动画（search状态下的背景动画）
    this.startSlowRoulette()

    // 监听页面可见性变化，在页面隐藏时清理资源
    this.handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('页面隐藏，暂停所有定时器和轮询')
        this.clearAllIntervals()
      } else {
        console.log('页面重新可见，恢复相应动画')
        // 如果是search状态，重新启动慢速轮盘
        if (this.status === 'search') {
          this.startSlowRoulette()
        }
      }
    }
    document.addEventListener('visibilitychange', this.handleVisibilityChange)
  },
  beforeUnmount() {
    console.log('xingyunshike页面即将卸载，开始清理资源...')

    // 清理所有定时器和轮询
    this.clearAllIntervals()

    // 移除页面可见性监听器
    if (this.handleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
      this.handleVisibilityChange = null
    }


    // 清理canvas资源
    if (this.canvas) {
      const ctx = this.canvas.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      }
      this.canvas = null
    }

    console.log('xingyunshike页面资源清理完成')
  },
  methods: {
    testBtn(value){
      if (value == 'l'){
        this.callcat('getFace')
      }else{
        this.callcat('createOver')
      }
      
    },
    // 播放切换音效
    playSwitchSound() {
      try {
        const audio = new Audio(switchSoundUrl)
        audio.volume = 0.5 // 设置音量为50%
        audio.play().catch(error => {
          console.warn('播放切换音效失败:', error)
        })
      } catch (error) {
        console.warn('创建音频对象失败:', error)
      }
    },
    callcat(value){
      let ip = this.serveIp[0]
      // ip = import.meta.env.VITE_ACTIVITY_XINGYUNSHIKE || ip
      axios.post(`http://${ip}:3030/setStatus`,{
        state: value
      })
    },
    async startGetImg(){
        let ips = []
        if (this.serveIp){
            ips = this.serveIp
        }else{
            let conf = await electronApi.config.getConfig()
            ips = conf.IPs
        }
        let promiseLst = []
        ips.map(e=>{
            let ip = e.split('.')
            ip[3] = '100'
            let ipStr = ip.join('.')
            promiseLst.push(axios.get(`http://${ipStr}:3030/getImg`))
        })
        let result = await Promise.race(promiseLst).then((e,index)=>{
            console.log(e)
            let str = e.config.url
            let ipStr = str.match(/http:\/\/(\d+\.\d+\.\d+\.\d+)/)[1]
            this.serveIp = [ipStr]
            return e.data
        }).catch(async e=>{
            // 由于http问题,此处会通过catch返回
            console.log(e)
            return e
        })
        return result
    },
    // 初始化图片列表
    initImageList() {
      this.imgList = []
      for (let i = 1; i <= 19; i++) {
        const imgRef = this.$refs[`preloadImg${i}`]
        if (imgRef && imgRef.src) {
          this.imgList.push(imgRef.src)
        }
      }
      // 设置初始显示图片为第2张
      if (this.imgList.length > 1) {
        this.nowUserImg = this.imgList[1]
      }
      console.log('初始化图片列表完成，共', this.imgList.length, '张图片')
    },

    // 视频初始化完成回调
    handleInitFinished(videoRef) {
      this.videoRef = videoRef
      this.canvas = document.createElement('canvas')
    },

    // 开始人脸检测
    startFaceDetection() {
        //改为从100ip上获取
        let getImg = async e=>{
            let imgStruct = await this.startGetImg()
            if (imgStruct.id && imgStruct.img && this.status == 'search'){ // imgStruct.id &&
                if (this.tempUserId !== 0 && this.tempUserId == imgStruct.id) //无历史数据)
                {

                }else{
                   // 有历史数据
                   if ((this.userLst[0]?.id)?(this.userLst[0]?.id == imgStruct.id): false){

                   }else{
                    this.tempUserId = imgStruct.id
                    this.selectedGender = imgStruct.gender === 1 ? 'male' : 'female'
                    // 开始轮盘效果
                    this.startRoulette()
                    this.detectedPhotoData = imgStruct.img
                    // 开始上传和生成
                    await this.startImageGeneration()
                   } //有历史数据)
                    
                }
                
            }
            this.timeout3031 = setTimeout(getImg, 1000)
        }
        getImg()
        return
      this.faceDetectionInterval = setInterval(async () => {
        if (this.status !== 'search' || !this.videoRef) return

        try {
          const imageData = await this.capturePhoto()
          if (imageData) {
            const result = await getPhotoFaceSexAndAge({ base64: imageData })
            if (result && result.length > 0 && result[0].detection._score >0.35) {
              this.faceDetectionResult = result[0] // 取第一个检测结果
              this.detectedPhotoData = imageData // 保存检测时的原始照片数据
              this.handleFaceDetected()
            }
          }
        } catch (error) {
          console.error('人脸检测失败:', error)
          // 不显示错误消息，继续检测
        }
      }, 1000) // 每秒检测一次
    },

    // 拍照获取base64图片
    async capturePhoto() {
      if (!this.videoRef || !this.canvas) return null

      const video = this.videoRef
      const canvas = this.canvas
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      const ctx = canvas.getContext('2d')
      ctx.drawImage(video, 0, 0, video.videoWidth, video.videoHeight)
      return canvas.toDataURL('image/jpeg')
    },

    // 处理检测到人脸
    async handleFaceDetected() {
      console.log('检测到人脸:', this.faceDetectionResult)

      // 停止人脸检测
      clearInterval(this.faceDetectionInterval)
      this.faceDetectionInterval = null

      // 根据检测结果设置性别
      if (this.faceDetectionResult.gender) {
        this.selectedGender = this.faceDetectionResult.gender === 'male' ? 'male' : 'female'
      }

      // 开始轮盘效果
      this.startRoulette()

      // 开始上传和生成
      await this.startImageGeneration()
    },

    // 开始慢速轮盘效果（search状态下的背景动画）
    startSlowRoulette() {
      // 只在search状态下运行
      if (this.status !== 'search') return

      // 清理之前的慢速轮盘
      if (this.slowRouletteInterval) {
        clearInterval(this.slowRouletteInterval)
      }

      const slowRouletteStep = () => {
        // 只在search状态下继续运行
        if (this.status !== 'search') {
          this.clearSlowRoulette()
          return
        }

        // 切换图片
        this.rouletteCurrentIndex = (this.rouletteCurrentIndex + 1) % this.imgList.length
        this.nowUserImg = this.imgList[this.rouletteCurrentIndex]

        // 随机改变searchLogo位置
        this.randomizeSearchLogoPosition()
      }

      // 立即执行一次
      slowRouletteStep()

      // 设置定时器，每1秒切换一次
      this.slowRouletteInterval = setInterval(slowRouletteStep, this.slowRouletteSpeed)
      console.log('慢速轮盘动画已启动')
    },

    // 清理慢速轮盘
    clearSlowRoulette() {
      if (this.slowRouletteInterval) {
        clearInterval(this.slowRouletteInterval)
        this.slowRouletteInterval = null
        console.log('慢速轮盘动画已停止')
      }
    },

    // 开始快速轮盘效果（loading状态下的加速动画）
    startRoulette() {
      // 先停止慢速轮盘
      this.clearSlowRoulette()

      this.callcat('getFace')
      this.status = 'loading'
      this.rouletteSpeed = 30 // 重置初始速度，更快
      this.rouletteCurrentIndex = 0

      const rouletteStep = () => {
        // 切换图片
        this.rouletteCurrentIndex = (this.rouletteCurrentIndex + 1) % this.imgList.length
        this.nowUserImg = this.imgList[this.rouletteCurrentIndex]

        // 随机改变searchLogo位置
        this.randomizeSearchLogoPosition()

        // 递减速度（线性递减）
        if (this.rouletteSpeed < this.rouletteMinSpeed) {
            if (this.rouletteSpeed<50){
                this.rouletteSpeed +=0.6
            }else{
                this.rouletteSpeed += 20
            }

        }

        // 设置下一次执行
        this.rouletteInterval = setTimeout(rouletteStep, this.rouletteSpeed)
      }

      rouletteStep()
      console.log('快速轮盘动画已启动')
    },

    // 随机改变searchLogo位置
    randomizeSearchLogoPosition() {
      const searchLogo = document.querySelector('.searchLogo')
      if (searchLogo) {
        const maxLeft = 60 // 最大左边距百分比
        const maxTop = 80 // 最大上边距百分比
        const randomLeft = Math.random() * maxLeft
        const randomTop = Math.random() * maxTop*0.8

        searchLogo.style.left = randomLeft + '%'
        searchLogo.style.top = randomTop + '%'
      }
    },

    // 开始图片生成
    async startImageGeneration() {
      try {
        // 使用人脸检测时保存的照片数据
        const imageData = this.detectedPhotoData
        if (!imageData) {
          ElMessage.error('获取照片失败')
          return
        }

        // 创建文件对象
        const file = new File([base64ToBlob(imageData)], `${generateUUID()}.jpg`, {
          type: 'image/jpeg'
        })

        // 上传图片
        const uploadResult = await uploadImg(file)
        if (!uploadResult || !uploadResult.objectKey) {
          ElMessage.error('图片上传失败')
          return
        }

        this.avatarId = uploadResult.avatarId
        this.objectKey = uploadResult.objectKey

        // 获取样式数据
        const useSingleStore = useSinglePhotoStore()
        let styleLst = useSingleStore.stylesData || []

        // // 如果没有样式数据，使用默认样式
        // if (!styleLst.length) {
        //   styleLst = this.getDefaultStyles()
        // }

        // 根据性别筛选样式
        const sex = this.selectedGender === 'female' ? 0 : 1
        let sameSexStyles = styleLst.filter((i) => i.sex === sex)

        if (!sameSexStyles.length) {
          // 如果没有对应性别的样式，使用所有样式
          sameSexStyles = styleLst
        }

        // 随机选择一个样式
        const randomStyle = sameSexStyles[Math.floor(Math.random() * sameSexStyles.length)]
        const selectedCode = randomStyle.code_2d || randomStyle.code || '001'

        // 开始生成
        await this.generateImage(selectedCode)

      } catch (error) {
        console.error('图片生成失败:', error)
        ElMessage.error('图片生成失败: ' + error.message)
      }
    },

    // 获取默认样式
    getDefaultStyles() {
      return [
        { code_2d: '001', sex: 1, name: '男性样式1' },
        { code_2d: '002', sex: 1, name: '男性样式2' },
        { code_2d: '003', sex: 0, name: '女性样式1' },
        { code_2d: '004', sex: 0, name: '女性样式2' },
        { code_2d: '005', sex: 1, name: '男性样式3' },
        { code_2d: '006', sex: 0, name: '女性样式3' },
        { code_2d: '007', sex: 0, name: '女性样式4' },
        { code_2d: '008', sex: 1, name: '男性样式4' },
        { code_2d: '009', sex: 0, name: '女性样式5' }
      ]
    },

    // 生成图片
    async generateImage(styleCode) {
      try {
        const taskId = new Date().getTime().toString()
        this.generationTaskId = taskId

        // 调用新的生成API
        const response = await axios.post('/api/v1.0/digital-avatar/task/2d/gen', {
          client_id: electronApi.globals.getClientId().screen_num,
          task_id: taskId,
          digital_avatar_id: this.avatarId,
          data: [
            {
              index: 0,
              styles: [`p_${styleCode}`],
              avatar_image: this.objectKey
            }
          ]
        })

        console.log('生成请求发送成功:', response)

        // 更新taskId为服务器返回的taskId
        this.generationTaskId = response.data.data.task_id

        // 开始轮询结果
        this.timers.pollTimeout = setTimeout(() => {
          this.pollGenerationResult()
        }, 3000) // 3秒后开始轮询

      } catch (error) {
        console.error('生成图片请求失败:', error)
        ElMessage.error('生成图片请求失败: ' + (error.message || '未知错误'))
      }
    },

    // 轮询生成结果
    pollGenerationResult() {
      this.generationInterval = setInterval(async () => {
        try {
          const response = await axios.get('/api/v1.0/digital-avatar/task/2d/get', {
            params: {
              "task_id": this.generationTaskId,
              "client_id": electronApi.globals.getClientId().screen_num
            }
          })

          console.log('轮询结果:', response.data)

          if (response.data && response.data.data && response.data.data.avatars) {
            const avatars = response.data.data.avatars
            // 查找第一个完成的样式
            const completedAvatar = avatars.find(avatar => {
              return avatar.styles.find(style => style.status === 2) // status 2 表示完成
            })

            if (completedAvatar) {
              const completedStyle = completedAvatar.styles.find(style => style.status === 2)
              if (completedStyle && completedStyle.result_url) {
                // 生成完成
                this.handleGenerationComplete(completedStyle)
              }
            }
          }
        } catch (error) {
          console.error('获取生成结果失败:', error)
          this.clearAllIntervals()
          if (error?.code === 4000) {
            ElMessage.error('获取结果失败：' + (error?.msg || '未知错误'))
            this.clearAllIntervals()
          }
        }
      }, 1000) // 每秒轮询一次
    },

    // 处理生成完成
    async handleGenerationComplete(result) {
      console.log('图片生成完成:', result)
      this.userId = this.tempUserId

      // 停止轮盘和轮询
      this.clearAllIntervals()

      // 更新状态
      this.status = 'result'
      this.callcat('createOver')
      // 使用人脸检测时保存的原始照片数据
      const originalPhoto = this.detectedPhotoData
      this.originalPhotoData = originalPhoto

      // 保存当前结果数据，用于跳转loading页面
      await this.saveCurrentResultData(result, originalPhoto)

      // 更新界面
      await this.updateResultDisplay(result)

      // 添加到用户列表
      this.addToUserList(result, originalPhoto)

      // 开始10秒倒计时
      this.startResultTimer()
    },

    // 更新结果显示
    async updateResultDisplay(result) {
      // 使用人脸检测时保存的原始照片
      const originalPhoto = this.detectedPhotoData

      // 等待图片加载完成
      const img = new Image()
      img.onload = () => {
        // 左侧显示生成结果
        this.nowUserImg = result.result_url

        // 右侧显示原始照片
        this.updateRightSideContent(originalPhoto)
      }
      img.src = result.result_url
    },

    // 更新右侧内容
    updateRightSideContent(originalPhoto) {
      const rightContent = this.$refs.videoBox
      if (rightContent) {
        // 隐藏视频和搜索相关元素
        const video = rightContent.querySelector('base-video')
        const searchLogo = rightContent.querySelector('.searchLogo')
        const searchText = rightContent.querySelector('.search')
        const trans = rightContent.querySelector('.trans')

        if (video) video.style.display = 'none'
        if (searchLogo) searchLogo.style.display = 'none'
        if (searchText) searchText.style.display = 'none'
        if (trans) trans.style.display = 'none'

        // 显示原始照片
        const originalImg = document.createElement('img')
        originalImg.src = originalPhoto
        originalImg.style.cssText = `
          width: 90%;
          height: 90%;
          margin-top: 12%;
          object-fit: contain;
        `
        rightContent.appendChild(originalImg)
      }
    },

    // 获取默认样式数据
    getDefaultStyles() {
      return [
        { code_2d: '001', sex: 1 }, // 男性默认样式
        { code_2d: '009', sex: 0 }, // 女性默认样式
        { code_2d: '005', sex: 1 },
        { code_2d: '106', sex: 0 },
        { code_2d: '113', sex: 1 }
      ]
    },

    // 保存当前结果数据
    async saveCurrentResultData(result, originalPhoto) {
      // 获取样式数据
      const useSingleStore = useSinglePhotoStore()
      let styleLst = useSingleStore.stylesData || this.getDefaultStyles()

      // 根据性别筛选样式
      const sex = this.selectedGender === 'female' ? 0 : 1
      let sameSexStyles = styleLst.filter((i) => i.sex === sex)

      if (!sameSexStyles.length) {
        sameSexStyles = styleLst
      }

      // 创建File对象
      const file = new File([base64ToBlob(originalPhoto)], `${generateUUID()}.jpg`, {
        type: 'image/jpeg'
      })

      this.currentResultData = {
        subjectFile: file,
        avatarId: this.avatarId,
        objectKey: this.objectKey,
        codes: sameSexStyles.map((i) => i['code_2d']),
        code: sameSexStyles[0]['code_2d'],
        gender: this.selectedGender,
        taskId: this.generationTaskId,
        timestamp: Date.now(),
        result: result.result_url,
        real: originalPhoto
      }
    },

    // 添加到用户列表
    addToUserList(result, originalPhoto) {
      const newItem = {
        id: this.userId,
        real: originalPhoto,
        result: result.result_url,
        timestamp: Date.now(),
        // 保存loading.vue所需的数据
        subjectFile: this.currentResultData?.subjectFile,
        avatarId: this.avatarId,
        objectKey: this.objectKey,
        codes: this.currentResultData?.codes || [],
        code: this.currentResultData?.code,
        gender: this.selectedGender,
        taskId: this.generationTaskId
      }

      this.userLst.unshift(newItem) // 添加到列表开头
      if (this.userLst.length>4){
        this.userLst = this.userLst.slice(0,4)
      }
      // 保存到sessionStorage
      sessionStorage.setItem('xingyun_userLst', JSON.stringify(this.userLst))

      console.log('已添加到用户列表:', newItem)
    },

    // 清理所有定时器和轮询
    clearAllIntervals() {
      console.log('清理所有定时器和轮询...')

      // 清理人脸检测定时器
      if (this.faceDetectionInterval) {
        clearInterval(this.faceDetectionInterval)
        this.faceDetectionInterval = null
        console.log('已清理人脸检测定时器')
      }
      // 3031轮询
      
      if (this.timeout3031) {
        clearTimeout(this.timeout3031)
        this.timeout3031 = null
        console.log('已清理快速轮盘动画定时器')
      }
      // 清理轮盘动画定时器
      if (this.rouletteInterval) {
        clearTimeout(this.rouletteInterval)
        this.rouletteInterval = null
        console.log('已清理快速轮盘动画定时器')
      }

      // 清理慢速轮盘动画定时器
      if (this.slowRouletteInterval) {
        clearInterval(this.slowRouletteInterval)
        this.slowRouletteInterval = null
        console.log('已清理慢速轮盘动画定时器')
      }

      // 清理图片生成轮询定时器
      if (this.generationInterval) {
        clearInterval(this.generationInterval)
        this.generationInterval = null
        console.log('已清理图片生成轮询定时器')
      }

      // 清理结果显示定时器
      if (this.resultTimeout) {
        clearTimeout(this.resultTimeout)
        this.resultTimeout = null
        console.log('已清理结果显示定时器')
      }

      // 清理其他定时器
      if (this.timers.initTimeout) {
        clearTimeout(this.timers.initTimeout)
        this.timers.initTimeout = null
        console.log('已清理初始化定时器')
      }

      if (this.timers.pollTimeout) {
        clearTimeout(this.timers.pollTimeout)
        this.timers.pollTimeout = null
        console.log('已清理轮询启动定时器')
      }

    },

    // 重置到初始状态
    resetToInitialState() {
      console.log('重置到初始状态...')

      // 清理所有定时器和轮询
      this.clearAllIntervals()

      // 重置状态数据
      this.status = 'search'
      this.faceDetectionResult = null
      this.generationTaskId = null
      this.avatarId = null
      this.objectKey = null
      this.nowUserImg = this.imgList.length > 1 ? this.imgList[1] : ''
      this.rouletteSpeed = 30
      this.rouletteCurrentIndex = 0
      this.resultStartTime = null
      this.currentResultData = null
      this.originalPhotoData = null
      this.detectedPhotoData = null

      // 重置定时器集合
      this.timers = {
        initTimeout: null,
        pollTimeout: null
      }

      // 重新启动慢速轮盘动画
      this.$nextTick(() => {
        this.startSlowRoulette()
      })

      // 重新显示视频和搜索元素
      const rightContent = this.$refs.videoBox
      if (rightContent) {
        const video = rightContent.querySelector('base-video')
        const searchLogo = rightContent.querySelector('.searchLogo')
        const searchText = rightContent.querySelector('.search')
        const trans = rightContent.querySelector('.trans')
        const originalImg = rightContent.querySelector('img:not(.searchLogo)')

        if (video) video.style.display = 'block'
        if (searchLogo) {
          searchLogo.style.display = 'block'
          searchLogo.style.left = '0'
          searchLogo.style.top = '-1000px'
        }
        if (searchText) {
          searchText.style.display = 'flex'
          // 重新设置搜索文本的HTML结构
          searchText.innerHTML = `
            <span class="search-left">》》</span>
            <span class="search-center">搜寻中</span>
            <span class="search-right">《《</span>
          `
        }
        if (trans) trans.style.display = 'block'
        if (originalImg && originalImg !== searchLogo) {
          originalImg.remove()
        }
      }

      // 重新开始人脸检测
      this.startFaceDetection()
    },

    // 处理内容区域点击
    handleContentClick() {
      if (this.status === 'result') {
        // 如果当前是结果状态，点击重新开始
        this.resetToInitialState()
      }
    },

    // 处理图片点击（左侧结果图或右侧原图）
    handleImageClick(type) {
        console.log(type)
      if (this.status === 'result') {
        this.jumpToLoading()
      }
    },

    // 处理用户列表点击
    handleUserListClick(item, index) {
        
        this.jumpToLoading(item)
        
    },

    // 处理关闭按钮点击
    handleCloseClick(index) {
      // 删除对应的用户数据
      this.userLst.splice(index, 1)
      // 更新sessionStorage
      sessionStorage.setItem('xingyun_userLst', JSON.stringify(this.userLst))
    },


    // 跳转到loading页面
    jumpToLoading(itemData = null) {
      const dataToUse = itemData || this.currentResultData
      if (!dataToUse) {
        console.error('没有可用的数据进行跳转')
        return
      }
      const file = new File([base64ToBlob(dataToUse.real)], `${generateUUID()}.jpg`, {
        type: 'image/jpeg'
      })
      console.log(file)
      // 构造跳转所需的数据
      const jumpData = {
        subjectFile: file || dataToUse.subjectFile,
        avatarId: dataToUse.avatarId,
        objectKey: dataToUse.objectKey,
        codes: dataToUse.codes || [],
        code: dataToUse.code,
        gender: dataToUse.gender,
        taskId: dataToUse.taskId
      }

      console.log('准备跳转到loading页面，数据:', jumpData)

      // 使用路由传值store存储数据
      const useRouteParams = useRouteParamsStore()
      useRouteParams.setRouteParams(jumpData)

      // 跳转到loading页面，只传递必要的query参数
      this.$router.push({
        path: '/rebuild2-loading',
        query: { code: jumpData.code }
      })
    },

    // 开始10秒倒计时
    startResultTimer() {
      this.resultStartTime = Date.now()

      // 清除之前的定时器
      if (this.resultTimeout) {
        clearTimeout(this.resultTimeout)
      }

      // 10秒后自动重置
      this.resultTimeout = setTimeout(() => {
        this.resetToInitialState()
      }, 10000)
    }
  }
}
</script>

<style scoped lang="less">
.llll{
  height: 100rem;
  width: 100rem;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10000;
}
.rrrr{
  height: 100rem;
  width: 100rem;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 10000;
}
.nowUserBox{
    position: absolute;
    z-index: 1;
    height: 1230rem;
    width: 90vw;
    top: 350rem;
    left: 5%;
    display: flex;
    flex-direction:row;
    .item{
        flex: 1;
        position: relative;
        padding: 50rem 20rem;
        .content{
            background-color:#c1f4ff;
            height: 100%;
            width: 100%;
            position: relative;
            .left{
                height: 90%;
                width: 90%;
                margin-top: 12%;
                object-fit: contain;
            }
            .right{
                object-fit: cover!important;
                z-index: 0!important;
                transform: scaleX(-1);
            }
            .searchLogo{
                position: absolute;
                left: 0;
                height: 18vw;
                width: 18vw;
                top: -1000px;
                z-index: 5;
                animation: rotate 2s linear infinite;
                transform-origin: center center;
            }
            .search{
                font-size: 70rem;
                font-weight: bold;
                color: #7bf619;
                position: absolute;
                bottom: 80rem;
                width: 100%;
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;

                .search-left {
                    animation: bounceLeft 1.5s ease-in-out infinite;
                }

                .search-center {
                    margin: 0 10rem;
                }

                .search-right {
                    animation: bounceRight 1.5s ease-in-out infinite;
                }
            }
            .trans{
                height: 8%;
                width: 100%;
                bottom: 0;
                position: absolute;
                background: linear-gradient(#00000000, #0bffff);
            }
        }
        .border{
            height: 100%;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            pointer-events: none;
        }
    }
}
.userBoxLst{
    // background-color: aqua;
    text-align: left;
    width: 94%;
    height: 650rem;
    position: absolute;
    bottom: 160rem;
    left: 50%;
    transform: translateX(-50%);
    .lstItem{
        display: inline-flex;
        flex-direction: row;
        padding: 30rem;
        height: 50%;
        width: 50%;
        box-sizing: border-box;
        position: relative;
        .close{
            position: absolute;
            right: 20rem;
            top: 20rem;
            z-index: 5;
            height: 4vw;
        }
        .l, .r{
            // flex: 1;
            height: 300rem;
            width: 300rem;
            position: relative;
            padding: 10rem;
            box-sizing: border-box;
            .content{
                height: 280rem;
                width: 280rem;
                background-color: #c1f4ff;
                img{
                    height: 100%;
                    width: 100%;
                    object-fit: contain;
                }
                .limg{
                    height: 70%;
                    width: 70%;
                    margin-top: 15%;
                    margin-left: 15%;
                    // position: absolute;
                    // top: 50%;
                    // left: 50%;
                    // transform: translate(-50%, -50%);
                }
                .rimg{
                    object-fit: cover;
                    transform: scaleX(-1);
                }
            }
            .border{
                height: 300rem;
                width: 300rem;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 2;
                pointer-events: none;
            }
        }
        .r{
            .content{
                background-color: #000;
            }
        }
    }
    .footer{
        position: absolute;
        bottom: -120rem;
        color: #fff;
        font-size: 45rem;
        font-weight: bold;
        width: 100%;
        text-align: center;
    }
}

// 动画关键帧
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounceLeft {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(-15rem);
    }
}

@keyframes bounceRight {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(15rem);
    }
}

.topBox{
    position: absolute;
    z-index: 2;
    top: 0;
    left: 50%;
    .popofifi{
        position: absolute;
        top: 40rem;
        left: 50%;
        transform: translateX(-50%);
    }
    .logoBox{
        width: 85vw;
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
    }
}

.bg{
    height: 100vh;
    width: 100vw;
}

</style>